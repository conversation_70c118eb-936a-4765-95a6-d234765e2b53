package com.btpnsyariah.agendaku.cashmanager.other.service;

import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerDTO;
import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerEntity;
import com.btpnsyariah.agendaku.cashmanager.other.repository.FlyerRepository;
import com.btpnsyariah.agendaku.cashmanager.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class FlyerService {

    @Autowired
    FlyerRepository flyerRepository;
    @Autowired
    MinioService minioService;
    private static final Logger LOGGER = LoggerFactory.getLogger(FlyerService.class);
    @Value("${minio.url}")
    String minioUrl;

    public void persistFlyer(MultipartFile file, FlyerDTO flyerDTOS, String token) throws Exception {
        if (file.isEmpty()) {
            throw new BadRequestException("File is Empty!");
        }

        String filePath = uploadFile(file, token);
        flyerRepository.save(FlyerEntity.builder()
                .title(flyerDTOS.getTitle())
                .description(flyerDTOS.getDescription())
                .dtActiveFrom(flyerDTOS.getDtActiveFrom())
                .dtActiveTo(flyerDTOS.getDtActiveTo())
                .apkVersion(flyerDTOS.getApkVersion())
                .fileName(filePath)
                .flyerOrder(flyerDTOS.getFlyerOrder())
                .isActive(flyerDTOS.getIsActive())
                .createdDate(new Timestamp(new Date().getTime()))
                .updatedDate(new Timestamp(new Date().getTime()))
                .build());
    }

    public String uploadFile(MultipartFile file,String token) throws Exception {
        String extension = Utility.getFileNameExtension(Objects.requireNonNull(file.getOriginalFilename()));
        String fileName = Constants.FLYER_STORAGE_PATH+"/"+Utility.fileNameGenerator(UUID.randomUUID().toString(),"")+"."+extension;
        return minioService.uploadFile(file, fileName,token);
    }

    public PageImpl getAllFlyers(String version, String startDate, String endDate, Pageable pageable) {
        if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
            Date dateFrom = Utility.convertToDate(startDate, new SimpleDateFormat("yyyy-MM-dd"));
            Date dateTo = Utility.convertToDate(endDate, new SimpleDateFormat("yyyy-MM-dd"));

            return Utility.paginationMapping(pageable, flyerRepository.findAllFlyerStartingInAndEndingInWithOptionalVersion(
                    dateFrom,
                    dateTo,
                    version
            ).stream().map(
                    n -> FlyerDTO.builder().
                            id(n.getId()).
                            title(n.getTitle()).
                            description(n.getDescription()).
                            dtActiveFrom(n.getDtActiveFrom()).
                            dtActiveTo(n.getDtActiveTo()).
                            apkVersion(n.getApkVersion()).
                            fileName(n.getFileName()).
                            flyerOrder(n.getFlyerOrder()).
                            isActive(n.getIsActive()).
                            createdDate(n.getCreatedDate()).
                            updatedDate(n.getUpdatedDate()).
                            build()
            ).collect(Collectors.toList()));
        } else {
            return Utility.paginationMapping(pageable, flyerRepository.findAllByApkVersionOrAll(version).stream().map(
                    n -> FlyerDTO.builder().
                            id(n.getId()).
                            title(n.getTitle()).
                            description(n.getDescription()).
                            dtActiveFrom(n.getDtActiveFrom()).
                            dtActiveTo(n.getDtActiveTo()).
                            apkVersion(n.getApkVersion()).
                            fileName(n.getFileName()).
                            flyerOrder(n.getFlyerOrder()).
                            isActive(n.getIsActive()).
                            createdDate(n.getCreatedDate()).
                            updatedDate(n.getUpdatedDate()).
                            build()
            ).collect(Collectors.toList()));
        }
    }

    public void deleteFlyer(Long id) {
        FlyerEntity flyerEntity = flyerRepository.findById(id).orElseThrow(() -> new DataNotFoundException(String.format("Flyer is not found with id %s", id)));
        flyerEntity.setIsActive(false);
        flyerEntity.setUpdatedDate(new Timestamp(new Date().getTime()));
        flyerRepository.save(flyerEntity);
    }

    public void updateFlyer(MultipartFile file, FlyerDTO flyerDTOS, String token) throws Exception {
        FlyerEntity flyerEntity = flyerRepository.findById(flyerDTOS.getId()).orElseThrow(() -> new DataNotFoundException(String.format("Flyer is not found with id %s", flyerDTOS.getId())));
        if (file != null && !file.isEmpty()) {
            String filePath = uploadFile(file, token);
            flyerEntity.setFileName(filePath);
        }
        flyerEntity.setTitle(flyerDTOS.getTitle());
        flyerEntity.setDescription(flyerDTOS.getDescription());
        flyerEntity.setDtActiveFrom(flyerDTOS.getDtActiveFrom());
        flyerEntity.setDtActiveTo(flyerDTOS.getDtActiveTo());
        flyerEntity.setApkVersion(flyerDTOS.getApkVersion());
        flyerEntity.setFlyerOrder(flyerDTOS.getFlyerOrder());
        flyerEntity.setUpdatedDate(new Timestamp(new Date().getTime()));

        flyerRepository.save(flyerEntity);
    }

    public List<FlyerDTO> getFlyerDetails(String date, String version) {
        Date dateFrom = Utility.convertToDate(date, new SimpleDateFormat("yyyy-MM-dd"));
        return flyerRepository.findAllFlyerStartingInAndEndingInWithOptionalVersion(dateFrom, dateFrom, version).stream().map(
                n -> FlyerDTO.builder().
                        id(n.getId()).
                        title(n.getTitle()).
                        description(n.getDescription()).
                        dtActiveFrom(n.getDtActiveFrom()).
                        dtActiveTo(n.getDtActiveTo()).
                        apkVersion(n.getApkVersion()).
                        fileName(minioUrl + "/" + n.getFileName()).
                        flyerOrder(n.getFlyerOrder()).
                        isActive(n.getIsActive()).
                        createdDate(n.getCreatedDate()).
                        updatedDate(n.getUpdatedDate()).
                        build()
        ).collect(Collectors.toList());
    }
}
