IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='<PERSON><PERSON><PERSON><PERSON>lan' AND xtype='U')
BEGIN

CREATE TABLE WithdrawalPlan (
	id bigint PRIMARY KEY NOT NULL,
	prsId bigint NULL,
	sentraId bigint NULL,
	customerId bigint NULL,
	customerCode varchar(50) COLLATE Latin1_General_CI_AS NULL,
	savingsId bigint NULL,
	savingsCode varchar(50) COLLATE Latin1_General_CI_AS NULL,
	withdrawalPlanAmount float NULL,
	createdBy varchar(50) COLLATE Latin1_General_CI_AS NULL,
	createdAt datetime2 NULL,
	createdById int NULL,
	nextPrsId bigint NULL,
	transactionDate date NULL,
	lastUpdatedAt datetime2 NULL,
	lastUpdatedBy varchar(20) COLLATE Latin1_General_CI_AS NULL,
	lastUpdatedById bigint NULL,
	updatedCount int NULL
);

END