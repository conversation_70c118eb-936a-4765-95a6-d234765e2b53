IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CoDailyContingency' AND xtype='U')
CREATE TABLE CoDailyContingency(
 id BIGINT primary key not null identity,
 TransactionType VARCHAR(20) not null,
 CoCode VARCHAR(20) not null,
 CoName VARCHAR(200),
 NIK VARCHAR(200),
 MMSCode VARCHAR(20),
 TransactionStatus VARCHAR(20),
 TotalPRSAmount DECIMAL(22,4) DEFAULT 0.0000,
 TotalCollectedAmount DECIMAL(22,4) DEFAULT 0.0000,
 Liquidity DECIMAL(22,4) DEFAULT 0.0000,
 LiquidityNotes VARCHAR(500),
 OtherTransaction DECIMAL(22,4) DEFAULT 0.0000,
 OtherTransactionNotes VARCHAR(500),
 TotalAmount DECIMAL(22,4),
 Hundreds INT DEFAULT 0,
 SeventyFives INT DEFAULT 0,
 Fiftys INT DEFAULT 0,
 Twentys INT DEFAULT 0,
 Tens INT DEFAULT 0,
 Fives INT DEFAULT 0,
 SmallMoney INT DEFAULT 0,
 CreatedDate DATETIME,
 CreatedBy VARCHAR(200),
 <PERSON><PERSON><PERSON>yId BIGINT,
 LB<PERSON><PERSON>yId BIGINT,
 TotalEstimatedPRSAmount DECIMAL(22,4) DEFAULT 0.0000,
 PRSNotes VARCHAR(500),
 TotalEstimatedCollectedAmount DECIMAL(22,4) DEFAULT 0.0000,
 AssignedKw VARCHAR(20),
 DailyTransactionType NVARCHAR(50),
 MobileId NVARCHAR(50),
 IdOnlineCoTransaction BIGINT
 )