UPDATE [dbo].[BalanceTransaction]
    SET
        [hundredThousands] = CASE WHEN [hundredThousands] IS NULL THEN 0 ELSE [hundredThousands] END,
        [fiftyThousands] = CASE WHEN [fiftyThousands] IS NULL THEN 0 ELSE [fiftyThousands] END,
        [twentyThousands] = CASE WHEN [twentyThousands] IS NULL THEN 0 ELSE [twentyThousands] END,
        [tenThousands] = CASE WHEN [tenThousands] IS NULL THEN 0 ELSE [tenThousands] END,
        [fiveThousands] = CASE WHEN [fiveThousands] IS NULL THEN 0 ELSE [fiveThousands] END,
        [twoThousands] = CASE WHEN [twoThousands] IS NULL THEN 0 ELSE [twoThousands] END,
        [oneThousands] = CASE WHEN [oneThousands] IS NULL THEN 0 ELSE [oneThousands] END,
        [fiveHundreds] = CASE WHEN [fiveHundreds] IS NULL THEN 0 ELSE [fiveHundreds] END,
        [twoHundreds] = CASE WHEN [twoHundreds] IS NULL THEN 0 ELSE [twoHundreds] END,
        [oneHundreds] = CASE WHEN [oneHundreds] IS NULL THEN 0 ELSE [oneHundreds] END
    WHERE
        [hundredThousands] IS NULL OR
        [fiftyThousands] IS NULL OR
        [twentyThousands] IS NULL OR
        [tenThousands] IS NULL OR
        [fiveThousands] IS NULL OR
        [twoThousands] IS NULL OR
        [oneThousands] IS NULL OR
        [fiveHundreds] IS NULL OR
        [twoHundreds] IS NULL OR
        [oneHundreds] IS NULL
;