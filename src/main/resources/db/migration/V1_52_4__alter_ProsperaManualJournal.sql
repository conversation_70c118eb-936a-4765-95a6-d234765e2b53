IF EXISTS (SELECT * FROM sysobjects WHERE name='ProsperaManualJournalLog' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.ProsperaManualJournalLog','Amount') IS NULL
    BEGIN
        ALTER TABLE ProsperaManualJournalLog ADD Amount DECIMAL(22,4) not null DEFAULT 0.0000;
    END
    IF COL_LENGTH('dbo.ProsperaManualJournalLog','Period') IS NULL
    BEGIN
        ALTER TABLE ProsperaManualJournalLog ADD Period NVARCHAR(10);
    END
    IF COL_LENGTH('dbo.ProsperaManualJournalLog','TransactionType') IS NULL
    BEGIN
        ALTER TABLE ProsperaManualJournalLog ADD TransactionType NVARCHAR(1000);
    END
END