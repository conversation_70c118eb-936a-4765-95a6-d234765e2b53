IF EXISTS (SELECT * FROM sysobjects WHERE name='BalanceTransaction' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.BalanceTransaction','SeventyFiveThousands') IS NULL
    ALTER TABLE BalanceTransaction ADD SeventyFiveThousands INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='CashOpnameTransaction' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CashOpnameTransaction','SeventyFiveThousands') IS NULL
    ALTER TABLE CashOpnameTransaction ADD SeventyFiveThousands INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='CashOpnameTransactionDifference' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CashOpnameTransactionDifference','SeventyFiveThousands') IS NULL
    ALTER TABLE CashOpnameTransactionDifference ADD SeventyFiveThousands INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='CoDailyTransaction' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CoDailyTransaction','SeventyFives') IS NULL
    ALTER TABLE CoDailyTransaction ADD SeventyFives INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='CoverDanaRequest' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CoverDanaRequest','SeventyFives') IS NULL
    ALTER TABLE CoverDanaRequest ADD SeventyFives BIGINT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='KWDailyTransaction' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.KWDailyTransaction','SeventyFives') IS NULL
    ALTER TABLE KWDailyTransaction ADD SeventyFives INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='KWReport' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.KWReport','SeventyFives') IS NULL
    ALTER TABLE KWReport ADD SeventyFives INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='LBDailyTransaction' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.LBDailyTransaction','SeventyFives') IS NULL
    ALTER TABLE LBDailyTransaction ADD SeventyFives INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='LBReport' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.LBReport','SeventyFives') IS NULL
    ALTER TABLE LBReport ADD SeventyFives INT DEFAULT 0 WITH VALUES;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='MoneyExchangeTransaction' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.MoneyExchangeTransaction','SeventyFiveThousands') IS NULL
    ALTER TABLE MoneyExchangeTransaction ADD SeventyFiveThousands INT DEFAULT 0 WITH VALUES;
END