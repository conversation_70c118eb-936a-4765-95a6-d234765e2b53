IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='<PERSON>BCMappingTUR' AND xtype='U')
BEGIN
    CREATE TABLE MsBCMappingTUR (
        ID varchar(20) COLLATE Latin1_General_CI_AS NOT NULL,
        DTPopulate datetime NULL,
        SysPopulate varchar(50) COLLATE Latin1_General_CI_AS NULL,
        FieldChecksum varchar(100) COLLATE Latin1_General_CI_AS NULL,
        MMSCode varchar(10) COLLATE Latin1_General_CI_AS NULL,
        MMSName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        BMCode varchar(10) COLLATE Latin1_General_CI_AS NULL,
        BMAreaName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        NIKBM varchar(50) COLLATE Latin1_General_CI_AS NULL,
        BMName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        SBMCode varchar(10) COLLATE Latin1_General_CI_AS NULL,
        SBMRegionName varchar(50) COLLATE Latin1_General_CI_AS NULL,
        SBMName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        NIKSBM varchar(20) COLLATE Latin1_General_CI_AS NULL,
        BCCode varchar(10) COLLATE Latin1_General_CI_AS NULL,
        BCRegionName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        NIKBC varchar(50) COLLATE Latin1_General_CI_AS NULL,
        BCName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        EmailBC varchar(1000) COLLATE Latin1_General_CI_AS NULL,
        DHCode varchar(10) COLLATE Latin1_General_CI_AS NULL,
        DHName varchar(100) COLLATE Latin1_General_CI_AS NULL,
        StatMMS int NULL,
        StatBM int NULL,
        StatBC int NULL,
        StatDH int NULL,
        gmt varchar(10) COLLATE Latin1_General_CI_AS NULL,
        CONSTRAINT PK__MsBCMapp__3214EC27871040E1 PRIMARY KEY (ID)
    );
END