IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ActivationReport' AND xtype='U')
BEGIN
CREATE TABLE ActivationReport(
 id BIGINT primary key not null identity,
 NIK VARCHAR(200),
 COName VARCHAR(200),
 MMSCode VARCHAR(20),
 MMSName VARCHAR(200),
 MobileId VARCHAR(50),
 CreatedDate DATETIME,
 ApprovedBy VARCHAR(200),
 Action VARCHAR(100)
)

  insert into ActivationReport (NIK,COName,MMSCode,MMSName,MobileId,CreatedDate,ApprovedBy,Action)
  SELECT  cu.NIK,cu.COName,mms.MMSCode,mms.MMSName,cu.MobileId,GETDATE(),'SYSTEM','ACTIVATION' from CashUserPersonas cu
  inner join CoverDanaMapping mms on mms.MMSCode =cu.MMSCode
  where cu.MobileId is not null and cu.MobileId != ''

END
