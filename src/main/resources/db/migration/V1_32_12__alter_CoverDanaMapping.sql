IF EXISTS (SELECT * FROM sysobjects WHERE name='CoverDanaMapping' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CoverDanaMapping','UpdateNoKomBy') IS NULL
    ALTER TABLE CoverDanaMapping ADD UpdateNoKomBy VARCHAR(200) NULL;
    IF COL_LENGTH('dbo.CoverDanaMapping','OtorNoKomBy') IS NULL
    ALTER TABLE CoverDanaMapping ADD OtorNoKomBy VARCHAR(200) NULL;
    IF COL_LENGTH('dbo.CoverDanaMapping','OldNoKom') IS NULL
    ALTER TABLE CoverDanaMapping ALTER COLUMN OldNoKom VARCHAR(200);
    IF COL_LENGTH('dbo.CoverDanaMapping','CurrentNoKom') IS NULL
    ALTER TABLE CoverDanaMapping ALTER COLUMN CurrentNoKom VARCHAR(200);
END