IF EXISTS (SELECT * FROM sysobjects WHERE name='FPBRequest' AND xtype='U')
BEGIN

    IF COL_LENGTH('dbo.FPBRequest','ActualizedAmount') IS NULL
    ALTER TABLE FPBRequest ADD ActualizedAmount DECIMAL(22,4) NOT NULL DEFAULT 0.0000;

    IF COL_LENGTH('dbo.FPBRequest','LbDailyTransactionId') IS NULL
    ALTER TABLE FPBRequest ADD LbDailyTransactionId BIGINT;

    IF COL_LENGTH('dbo.FPBRequest','AdditionalSettlementId') IS NULL
    ALTER TABLE FPBRequest ADD AdditionalSettlementId BIGINT;

    ALTER TABLE FPBRequest ADD CONSTRAINT FK_LbDailyTransactionId
    FOREIGN KEY (LbDailyTransactionId) REFERENCES LbDailyTransaction(id);

    ALTER TABLE FPBRequest ADD CONSTRAINT FK_AdditionalSettlementId
    FOREIGN KEY (AdditionalSettlementId) REFERENCES LbDailyTransaction(id);

END