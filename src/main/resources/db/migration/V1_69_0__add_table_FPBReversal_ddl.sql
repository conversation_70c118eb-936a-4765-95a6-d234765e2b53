IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FPBReversal' AND xtype='U')
  CREATE TABLE FPBReversal (
  	Id bigint PRIMARY KEY NOT NULL IDENTITY,
  	RequisitionId BIGINT NOT NULL,
  	CreatedDate DATETIME,
  	ApprovalLog BIGINT NOT NULL,
  	SettledAmount DECIMAL(22,4) NOT NULL DEFAULT 0.0000,
  	ReverseSettlementId BIGINT,
  	AdditionalSettlementId BIGINT,
  	CONSTRAINT FK_ApprovalLog FOREIGN KEY (ApprovalLog)
    REFERENCES dbo.FPBTransactionLog(Id),
  	CONSTRAINT FK_RequisitionId FOREIGN KEY (RequisitionId)
  	REFERENCES dbo.FPBRequest(Id),
  	CONSTRAINT FK_ReverseSettlementId FOREIGN KEY (ReverseSettlementId)
    REFERENCES dbo.LbDailyTransaction(id),
  	CONSTRAINT FK_AdditionalReverseSettlementId FOREIGN KEY (AdditionalSettlementId)
    REFERENCES dbo.LbDailyTransaction(id)
  );