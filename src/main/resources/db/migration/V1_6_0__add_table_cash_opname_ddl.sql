IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashOpnameTransaction' AND xtype='U')
  CREATE TABLE CashOpnameTransaction (
            id bigint primary key not null identity,
            mmsCode varchar (50),
            cashOpnameType varchar (50),
            CreatedDate DATETIME,
            CreatedBy VARCHAR(50),
            UpdatedDate DATETIME,
            UpdatedBy VARCHAR(50),
            hundredThousands INT DEFAULT (0),
            fiftyThousands INT DEFAULT (0),
            twentyThousands INT DEFAULT (0),
            tenThousands INT DEFAULT (0),
            fiveThousands INT DEFAULT (0),
            twoThousands INT DEFAULT (0),
            oneThousands INT DEFAULT (0),
            fiveHundreds INT DEFAULT (0),
            twoHundreds INT DEFAULT (0),
            oneHundreds INT DEFAULT (0),
            smallMoney INT DEFAULT (0),
            TotalAmount DECIMAL(22,4) DEFAULT 0.0000
  )