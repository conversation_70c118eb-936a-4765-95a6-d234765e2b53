IF COL_LENGTH('dbo.CoverDanaRequest','Thousand<PERSON>oi<PERSON>') IS NULL
BEGIN
    ALTER TABLE CoverDanaRequest ADD ThousandCoin BIGINT NOT NULL DEFAULT 0 WITH VALUES;
END

IF COL_LENGTH('dbo.CoverDanaRequest','TwoHundredCoin') IS NULL
BEGIN
    ALTER TABLE CoverDanaRequest ADD TwoHundredCoin BIGINT NOT NULL DEFAULT 0 WITH VALUES;
END

IF COL_LENGTH('dbo.CoverDanaRequest','OneHundredCoin') IS NULL
BEGIN
    ALTER TABLE CoverDanaRequest ADD OneHundredCoin BIGINT NOT NULL DEFAULT 0 WITH VALUES;
END

IF COL_LENGTH('dbo.CoverDanaRequest','FiftyCoin') IS NULL
BEGIN
    ALTER TABLE CoverDanaRequest ADD FiftyCoin BIGINT NOT NULL DEFAULT 0 WITH VALUES;
END

IF COL_LENGTH('dbo.CoverDanaRequest','TwentyFiveCoin') IS NULL
BEGIN
    ALTER TABLE CoverDanaRequest ADD TwentyFiveCoin BIGINT NOT NULL DEFAULT 0 WITH VALUES;
END
