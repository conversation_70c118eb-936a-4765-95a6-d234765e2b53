IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FlyerConfig' AND xtype='U')
  CREATE TABLE FlyerConfig (
  	Id bigint PRIMARY KEY NOT NULL IDENTITY,
  	Title NVARCHAR(200) NOT NULL,
  	Description NVARCHAR(200),
  	DtActiveFrom DATE,
  	DtActiveTo DATE,
  	ApkVersion NVARCHAR(20) NOT NULL,
  	FileName NVARCHAR(200) NOT NULL,
  	FlyerOrder INTEGER,
  	IsActive BIT,
  	CreatedDate DATETIME,
  	UpdatedDate DATETIME,
  );
